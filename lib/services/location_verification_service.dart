import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:latlong2/latlong.dart';

/// 位置验证服务
///
/// 功能：
/// - GPS位置获取
/// - 实地验证
/// - 位置精度检查
class LocationVerificationService {
  static const double _onSiteThresholdMeters = 50.0; // 实地验证阈值：50米

  /// 获取当前位置
  static Future<Position?> getCurrentPosition() async {
    try {
      debugPrint('🔍 [位置验证] 开始获取当前位置');

      // 检查位置权限
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        debugPrint('❌ [位置验证] 位置服务未启用');
        return null;
      }

      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        permission = await Geolocator.requestPermission();
        if (permission == LocationPermission.denied) {
          debugPrint('❌ [位置验证] 位置权限被拒绝');
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        debugPrint('❌ [位置验证] 位置权限被永久拒绝');
        return null;
      }

      // 获取当前位置
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      debugPrint('✅ [位置验证] 获取位置成功');
      debugPrint('🔍 [位置验证] 纬度: ${position.latitude}');
      debugPrint('🔍 [位置验证] 经度: ${position.longitude}');
      debugPrint('🔍 [位置验证] 精度: ${position.accuracy}米');

      return position;
    } catch (e) {
      debugPrint('❌ [位置验证] 获取位置失败: $e');
      return null;
    }
  }

  /// 验证是否在钓点附近（实地验证）
  static bool isOnSite(LatLng spotLocation, Position currentPosition) {
    final distance = Geolocator.distanceBetween(
      spotLocation.latitude,
      spotLocation.longitude,
      currentPosition.latitude,
      currentPosition.longitude,
    );

    final isOnSite = distance <= _onSiteThresholdMeters;

    debugPrint('🔍 [位置验证] 钓点位置: ${spotLocation.latitude}, ${spotLocation.longitude}');
    debugPrint('🔍 [位置验证] 当前位置: ${currentPosition.latitude}, ${currentPosition.longitude}');
    debugPrint('🔍 [位置验证] 距离: ${distance.toStringAsFixed(1)}米');
    debugPrint('🔍 [位置验证] 实地验证: $isOnSite (阈值: $_onSiteThresholdMeters米)');

    return isOnSite;
  }

  /// 获取位置精度等级
  static String getAccuracyLevel(double accuracy) {
    if (accuracy <= 5) {
      return '高精度';
    } else if (accuracy <= 10) {
      return '中等精度';
    } else if (accuracy <= 20) {
      return '低精度';
    } else {
      return '精度较差';
    }
  }

  /// 检查位置精度是否足够
  static bool isAccuracyGood(double accuracy) {
    return accuracy <= 20; // 20米以内认为精度足够
  }

  /// 创建发布位置信息
  static Map<String, dynamic> createPublishLocationInfo(
    LatLng spotLocation,
    Position currentPosition,
  ) {
    final distance = Geolocator.distanceBetween(
      spotLocation.latitude,
      spotLocation.longitude,
      currentPosition.latitude,
      currentPosition.longitude,
    );

    final isOnSite = distance <= _onSiteThresholdMeters;

    return {
      'publish_latitude': currentPosition.latitude,
      'publish_longitude': currentPosition.longitude,
      'publish_accuracy': currentPosition.accuracy,
      'publish_timestamp': DateTime.now().toIso8601String(),
      'distance_to_spot': distance,
      'is_on_site': isOnSite,
      'accuracy_level': getAccuracyLevel(currentPosition.accuracy),
      'is_accuracy_good': isAccuracyGood(currentPosition.accuracy),
    };
  }

  /// 监听位置变化（用于实时更新）
  static StreamSubscription<Position>? listenToLocationChanges(
    LatLng spotLocation,
    Function(bool isOnSite, Position position) onLocationUpdate,
  ) {
    try {
      return Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10, // 10米变化才触发更新
        ),
      ).listen((Position position) {
        final isOnSite = LocationVerificationService.isOnSite(
          spotLocation,
          position,
        );
        onLocationUpdate(isOnSite, position);
      });
    } catch (e) {
      debugPrint('❌ [位置验证] 监听位置变化失败: $e');
      return null;
    }
  }
}
