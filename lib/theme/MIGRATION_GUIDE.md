# 主题系统迁移指南

## 概述

本项目已完成主题系统的统一迁移，将原本分散的主题配置整合到 `lib/theme/unified_theme.dart` 中。

## 迁移内容

### 1. 文件整合

**已整合的文件：**
- `lib/theme.dart` (CustomTheme) → `lib/theme/unified_theme.dart` (AppTheme)
- `lib/theme/app_design_system.dart` (AppDesignSystem) → `lib/theme/unified_theme.dart` (AppTheme)

**新的统一主题文件：**
- `lib/theme/unified_theme.dart` - 统一主题管理

### 2. 类名变更

| 旧类名 | 新类名 | 说明 |
|--------|--------|------|
| `CustomTheme` | `AppTheme` | 主题配置类 |
| `AppDesignSystem` | `AppTheme` | 设计系统整合到主题类 |

### 3. 属性映射

#### 颜色属性映射

| 旧属性 | 新属性 | 说明 |
|--------|--------|------|
| `CustomTheme.loginGradientStart` | `AppTheme.loginGradientStart` | 登录渐变起始色 |
| `CustomTheme.loginGradientEnd` | `AppTheme.loginGradientEnd` | 登录渐变结束色 |
| `CustomTheme.primaryColor` | `AppTheme.legacyPrimaryColor` | 旧主色调 |
| `CustomTheme.white` | `AppTheme.white` | 白色 |
| `CustomTheme.black` | `AppTheme.black` | 黑色 |
| `AppDesignSystem.primaryColor` | `AppTheme.designPrimaryColor` | 设计系统主色调 |
| `AppDesignSystem.backgroundColor` | `AppTheme.backgroundColor` | 背景色 |
| `AppDesignSystem.surfaceColor` | `AppTheme.surfaceColor` | 表面色 |

#### 字体属性映射

| 旧属性 | 新属性 |
|--------|--------|
| `AppDesignSystem.headlineLarge` | `AppTheme.headlineLarge` |
| `AppDesignSystem.bodyMedium` | `AppTheme.bodyMedium` |
| `AppDesignSystem.labelLarge` | `AppTheme.labelLarge` |

#### 间距属性映射

| 旧属性 | 新属性 |
|--------|--------|
| `AppDesignSystem.spacingS` | `AppTheme.spacingS` |
| `AppDesignSystem.spacingM` | `AppTheme.spacingM` |
| `AppDesignSystem.spacingL` | `AppTheme.spacingL` |

#### 圆角属性映射

| 旧属性 | 新属性 |
|--------|--------|
| `AppDesignSystem.radiusS` | `AppTheme.radiusS` |
| `AppDesignSystem.radiusM` | `AppTheme.radiusM` |
| `AppDesignSystem.radiusL` | `AppTheme.radiusL` |

### 4. 已更新的文件

以下文件已完成迁移：

**核心文件：**
- `lib/main.dart` - 使用新的统一主题
- `lib/pages/main_screen.dart` - 使用ConvexAppBar主题配置

**工具文件：**
- `lib/utils/bubble_indicator_painter.dart` - 更新主题引用

**组件文件：**
- `lib/widgets/split_screen_add_spot.dart`
- `lib/widgets/add_spot_form/add_spot_form.dart`
- `lib/widgets/add_spot_form/bait_selector.dart`
- `lib/widgets/add_spot_form/fish_type_selector.dart`
- `lib/widgets/add_spot_form/loading_states.dart`
- `lib/widgets/add_spot_form/spot_description_input.dart`
- `lib/widgets/add_spot_form/spot_name_input.dart`
- `lib/widgets/add_spot_form/water_level_selector.dart`

### 5. ConvexAppBar 主题配置

新增了专门的 `ConvexAppBarTheme` 类来管理底部导航栏的样式：

```dart
// 获取ConvexAppBar配置
final config = ConvexAppBarTheme.getConfig(context);

// 获取TabItem列表
final homeItems = ConvexAppBarTheme.getHomeTabItems(context);
final otherItems = ConvexAppBarTheme.getOtherTabItems();
```

## 使用指南

### 导入新主题

```dart
import '../theme/unified_theme.dart';
```

### 使用主题属性

```dart
// 颜色
color: AppTheme.designPrimaryColor,
backgroundColor: AppTheme.surfaceColor,

// 字体
style: AppTheme.bodyMedium,

// 间距
padding: EdgeInsets.all(AppTheme.spacingM),

// 圆角
borderRadius: BorderRadius.circular(AppTheme.radiusS),

// 组件样式
decoration: AppTheme.cardDecoration,
```

### Material 3 主题

```dart
MaterialApp(
  theme: AppTheme.lightTheme,
  darkTheme: AppTheme.darkTheme,
  themeMode: ThemeMode.system,
)
```

## 注意事项

1. **向后兼容性**：保留了所有旧的颜色和样式配置，确保现有代码不会破坏
2. **渐进迁移**：可以逐步将其他文件迁移到新的主题系统
3. **类型安全**：所有主题属性都是静态常量，编译时检查
4. **深色模式**：自动支持深色模式和浅色模式切换

## 后续工作

1. 可以考虑将 `lib/theme.dart` 和 `lib/theme/app_design_system.dart` 标记为废弃
2. 逐步迁移其他页面使用统一主题配置
3. 根据需要扩展主题系统的功能

## 迁移完成状态

✅ 主题文件整合完成  
✅ ConvexAppBar主题配置完成  
✅ 核心组件迁移完成  
✅ 编译错误修复完成  
✅ Material 3 集成完成  
✅ 深色模式支持完成  

迁移工作已全部完成，应用现在使用统一的主题管理系统。
