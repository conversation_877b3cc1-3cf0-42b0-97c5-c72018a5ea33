import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:convex_bottom_bar/convex_bottom_bar.dart';
import 'home_page.dart';
import 'search_page.dart';
import 'message/message_main_page.dart';
import 'profile_page.dart';
import '../services/service_locator.dart';
import '../theme/unified_theme.dart';

class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> with WidgetsBindingObserver {
  int _currentIndex = 0;
  bool _showBottomNavigationBar = true; // 控制底部导航栏显示

  // GlobalKey用于访问HomePage实例
  final GlobalKey<State<HomePage>> _homePageKey = GlobalKey<State<HomePage>>();

  // 页面列表
  late final List<Widget> _pages;

  // 使用新的服务架构
  // 通过Services便捷访问器访问认证服务

  @override
  void initState() {
    super.initState();

    // 添加应用生命周期监听
    WidgetsBinding.instance.addObserver(this);

    // 直接创建页面列表
    _pages = [
      HomePage(
        key: _homePageKey,
        onBottomNavigationBarVisibilityChanged: (visible) {
          debugPrint('🔍 [MainScreen] 收到导航栏显示状态变更请求: $visible');
          debugPrint('🔍 [MainScreen] 当前页面索引: $_currentIndex');
          // 只有当前显示的是HomePage（index=0）时才处理导航栏隐藏
          if (_currentIndex == 0) {
            setState(() {
              _showBottomNavigationBar = visible;
              debugPrint(
                '🔍 [MainScreen] 导航栏状态已更新为: $_showBottomNavigationBar',
              );
            });
          } else {
            debugPrint('🔍 [MainScreen] 当前不在HomePage，忽略导航栏状态变更请求');
          }
        },
      ),
      const SearchPage(),
      const MessageMainPage(),
      const ProfilePage(),
    ];
  }

  /// 根据当前页面动态生成TabItem列表
  List<TabItem> _getTabItems() {
    if (_currentIndex == 0) {
      // 主页：5个按钮，使用主题管理的配置
      return ConvexAppBarTheme.getHomeTabItems(context);
    } else {
      // 其他页面：4个按钮，使用主题管理的配置
      return ConvexAppBarTheme.getOtherTabItems();
    }
  }

  /// 获取当前活跃的索引（用于ConvexAppBar显示）
  int _getActiveIndex() {
    if (_currentIndex == 0) {
      return 0; // 主页模式：显示第0个按钮（主页）
    } else {
      // 其他页面模式（4按钮）：需要映射索引
      // 页面索引: 0(主页) 1(搜索) 2(消息) 3(我的)
      // 4按钮模式索引: 0(主页) 1(搜索) 2(消息) 3(我的)
      if (_currentIndex >= 2) {
        // 消息页和我的页：页面索引2,3 -> 按钮索引2,3
        return _currentIndex;
      } else {
        // 搜索页：页面索引1 -> 按钮索引1
        return _currentIndex;
      }
    }
  }

  /// 处理ConvexAppBar的点击事件
  void _handleTabTap(int index) {
    if (_currentIndex == 0 && index == 2) {
      // 主页模式下点击添加钓点按钮
      _triggerAddSpotMode();
      return;
    }

    // 处理正常的页面切换
    int targetPageIndex;
    if (_currentIndex == 0) {
      // 从主页切换：需要映射索引
      // 5按钮模式索引: 0(主页) 1(搜索) 2(添加钓点) 3(消息) 4(我的)
      // 页面索引: 0(主页) 1(搜索) X(跳过) 2(消息) 3(我的)
      if (index > 2) {
        targetPageIndex = index - 1; // 消息和我的页面
      } else {
        targetPageIndex = index; // 主页和搜索页面
      }
    } else {
      // 从其他页面切换：直接映射
      targetPageIndex = index;
    }

    setState(() {
      final oldIndex = _currentIndex;
      _currentIndex = targetPageIndex;

      // 如果切换到HomePage，检查是否需要隐藏导航栏
      if (targetPageIndex == 0 && oldIndex != 0) {
        // 切换到HomePage时，导航栏状态由HomePage自己控制
        debugPrint('🔍 [MainScreen] 切换到HomePage');
      }

      // 如果从HomePage切换到其他页面，确保显示导航栏
      if (oldIndex == 0 && targetPageIndex != 0) {
        _showBottomNavigationBar = true;
        debugPrint('🔍 [MainScreen] 从HomePage切换到其他页面，强制显示导航栏');
      }
    });
  }

  /// 触发添加钓点模式
  void _triggerAddSpotMode() {
    debugPrint('🔍 [MainScreen] 触发添加钓点模式');

    // 确保当前在主页，如果不在主页则先切换到主页
    if (_currentIndex != 0) {
      setState(() {
        _currentIndex = 0;
      });
      // 等待页面切换完成后再调用添加钓点功能
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _callHomePageAddSpot();
      });
    } else {
      // 已经在主页，直接调用添加钓点功能
      _callHomePageAddSpot();
    }
  }

  /// 调用HomePage的添加钓点功能
  void _callHomePageAddSpot() {
    final homePageState = _homePageKey.currentState;
    if (homePageState != null) {
      // 通过dynamic调用triggerAddSpotMode方法
      (homePageState as dynamic).triggerAddSpotMode();
      debugPrint('🔍 [MainScreen] 已调用HomePage的添加钓点功能');
    } else {
      debugPrint('🔍 [MainScreen] 无法获取HomePage实例');
    }
  }

  @override
  void dispose() {
    // 移除应用生命周期监听
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.resumed:
        // 应用从后台恢复时，静默尝试刷新登录状态
        _refreshAuthStateOnResume();
        break;
      case AppLifecycleState.paused:
        // 应用切换到后台时的处理
        debugPrint('应用切换到后台');
        break;
      case AppLifecycleState.detached:
        // 应用即将终止时的处理
        debugPrint('应用即将终止');
        break;
      case AppLifecycleState.inactive:
        // 应用失去焦点时的处理
        debugPrint('应用失去焦点');
        break;
      case AppLifecycleState.hidden:
        // 应用被隐藏时的处理
        debugPrint('应用被隐藏');
        break;
    }
  }

  /// 应用恢复时刷新认证状态
  Future<void> _refreshAuthStateOnResume() async {
    try {
      // 静默尝试刷新登录状态，不显示任何错误提示
      if (!Services.auth.isLoggedIn) {
        await Services.auth.initialize();
      }
    } catch (e) {
      // 静默处理错误，不影响用户体验
      debugPrint('应用恢复时刷新认证状态失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    // 只在导航栏状态变化时打印调试信息
    return PopScope(
      canPop: false, // 阻止默认的返回行为
      onPopInvokedWithResult: (didPop, result) {
        if (!didPop) {
          // 按返回键时将应用切换到后台，而不是退出
          _moveAppToBackground();
        }
      },
      child: Scaffold(
        body: IndexedStack(index: _currentIndex, children: _pages),
        bottomNavigationBar:
            _showBottomNavigationBar ? _buildConvexAppBar() : null,
      ),
    );
  }

  /// 构建ConvexAppBar
  Widget _buildConvexAppBar() {
    final config = ConvexAppBarTheme.getConfig(context);

    return ConvexAppBar(
      key: ValueKey('convex_bar_${_getTabItems().length}'), // 基于按钮数量的动态key
      style: config.style,
      items: _getTabItems(),
      initialActiveIndex: _getActiveIndex(),
      onTap: _handleTabTap,
      backgroundColor: config.backgroundColor,
      activeColor: config.activeColor,
      color: config.color,
      height: config.height,
      elevation: config.elevation,
    );
  }

  /// 将应用切换到后台
  void _moveAppToBackground() {
    SystemNavigator.pop();
  }
}
